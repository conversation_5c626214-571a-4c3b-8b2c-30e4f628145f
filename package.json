{"name": "hello-world", "version": "0.1.0", "private": true, "scripts": {"serve": "vue-cli-service serve", "build": "vue-cli-service build", "lint": "vue-cli-service lint"}, "dependencies": {"@hufe921/canvas-editor": "^0.9.113", "core-js": "^3.8.3", "vue": "^2.6.14", "vue-class-component": "^7.2.3", "vue-property-decorator": "^9.1.2"}, "devDependencies": {"@babel/core": "^7.12.16", "@babel/eslint-parser": "^7.12.16", "@typescript-eslint/eslint-plugin": "^5.4.0", "@typescript-eslint/parser": "^5.4.0", "@vue/cli-plugin-babel": "~5.0.0", "@vue/cli-plugin-eslint": "~5.0.0", "@vue/cli-plugin-typescript": "~5.0.0", "@vue/cli-service": "~5.0.0", "@vue/eslint-config-typescript": "^9.1.0", "eslint": "^7.32.0", "eslint-plugin-vue": "^8.0.3", "typescript": "~4.5.5", "vue-template-compiler": "^2.6.14"}, "eslintConfig": {"root": true, "env": {"node": true}, "extends": ["plugin:vue/essential", "eslint:recommended", "@vue/typescript"], "parserOptions": {"parser": "@typescript-eslint/parser"}, "rules": {}}, "browserslist": ["> 1%", "last 2 versions", "not dead"]}